<template>
  <div ref="sectionScroll" class="chat-view__section mac-scrollbar">
    <div
      ref="sectionContent"
      class="chat-view__content min-w-0 transition-cusbezier-300"
    >
      <el-skeleton

        v-if="skeletonVisible"
        class="chat_item-style chat_item"
        :class="{
          'chat_item-artifacts': artifactsVisible,
          'chat_item_margin': isResizablePanelVisible,
        }"
        :style="{
          '--el-skeleton-circle-size': isPc ? '50px' : '36px',
        }"
      >
        <template #template>
          <div v-for="index in 10" :key="index" class="mb-[50px] flex">
            <div class="shrink">
              <el-skeleton-item variant="circle" />
            </div>
            <el-skeleton class="ml-[20px]" :rows="2" animated />
          </div>
        </template>
      </el-skeleton>
      <div
        v-for="(item, index) in sections"
        :key="item.unionid"
        v-loading.fullscreen.lock="isBatchLoading"
        element-loading-custom-class="custom-loading"
        element-loading-text="Loading..."
        class="chat_item flex"
        :class="{
          'chat-view__content__bot': item.receiving,
          'chat-view__content__self': !item.receiving,
          'chat_item-artifacts': artifactsVisible,
          'lazy': isBatchLoading,
          'chat_item_margin': isResizablePanelVisible,
        }"
      >
        <div class="chat_item-style flex-1 break-words" :class="{ loading: loading && index === sections.length - 1 }">
          <div class="flex">
            <div class="group">
              <div class="sticky top-10px">
                <AvatarFrame
                  :src="item.avatar"
                  name="chat-view__section__avatar chat-view__section__avatar__margin"
                  :shadow="true"
                />
              </div>
            </div>
            <div class="no-scrollbar chat-view__section__right flex-1 pt-2px lt-md:(overflow-x-auto overflow-y-hidden pl-6px)" :class="{ 'answer-content': item.nickName === 'Juchats' }">
              <div
                class="chat-view__section__date flex items-center font-Lexend"
                :class="{
                  'mb-4px': !item.receiving,
                }"
              >
                <div class="text-16px font-700">
                  {{
                    item.receiving
                      ? "Juchats"
                      : isPc
                        ? item.nickName
                        : "You"
                  }}
                </div>
                <span class="ml-2 mt-2px flex-y-c text-3 text-primary-800">
                  {{ item.receiving
                    ? item.modelShowName
                      ? item.modelShowName
                      : currentModelString
                    : '' }}
                </span>
                <div
                  v-if="item.children?.length > 1"
                  class="ml-[5px] flex items-center"
                >
                  <div
                    class="cursor-pointer"
                    @click="
                      changePage({
                        type: 'prev',
                        unionid: item.unionid,
                        length: item.children.length,
                      })
                    "
                  >
                    <i class="i-ri-arrow-drop-left-line mb-3px"></i>
                  </div>
                  <div class="text-xs">
                    <span class="px-1">{{ item.page }}</span>
                    <span>/</span>
                    <span class="px-1">{{ item.children.length }}</span>
                  </div>
                  <div
                    class="cursor-pointer"
                    @click="
                      changePage({
                        type: 'next',
                        unionid: item.unionid,
                        length: item.children.length,
                      })
                    "
                  >
                    <i class="i-ri-arrow-drop-right-line mb-3px"></i>
                  </div>
                </div>
              </div>
              <div
                v-if="
                  item.modelType === MODE_TYPE.LEPTON_AUDIO
                    || !item.file
                    || !item.file.length
                "
                class="chat-view__markdown text-sm leading-30px"
              >
                <div v-if="getContent(item.content).array.length">
                  <div
                    :data-id="item.id"
                    @mousemove="e => handleContentIndex(e, item)"
                    @click="e => handleContentIndex(e, item)"
                    v-html="
                      formatMarkdown({
                        string: getContent(item.content).string,
                        receiving: item.receiving,
                      })
                    "
                  ></div>
                  <div v-if="isSmiles(item.content)" class="mt-4">
                    <SmilesRenderer :smiles="extractSmiles(item.content)" />
                  </div>
                  <!-- 这个操作按钮组只会显示在联网内容的下方 -->
                  <ChatContentItemAction
                    v-bind="getChatActionProps(item, index)"
                    :is-content-analyze-panel-visible="contentAnalyzePanelVisible"
                    v-on="getChatActionListeners(item)"
                  />
                </div>
                <div v-else-if="!item.receiving">
                  <div
                    v-if="editingItem && editingItem.unionid === item.unionid"
                    bg="#fff dark:#272727"
                    class="relative rounded-5px pl-16px pt-19px shadow-[0px_5px_20px_#00000012]"
                  >
                    <div
                      w="20px"
                      h="20px"
                      text="[var(--attachment-close-button-icon)]"
                      bg="[var(--attachment-close-button-bg)]"
                      class="absolute z-1 box-border flex-c cursor-pointer rounded-full -right-8px -top-8px"
                      @click="cancelEdit()"
                    >
                      <i class="i-mingcute-close-fill text-10px text-#A8AFBF dark:text-#727272" />
                    </div>
                    <el-input
                      v-model="editingContent"
                      :autosize="{ minRows: 2, maxRows: 20 }"
                      type="textarea"
                      placeholder="Please input"
                      class="editor-chat-wrapper"
                    />

                    <div class="mt-2 flex justify-end gap-10px pb-14px pr-14px">
                      <el-button
                        class="font-Lexend !h-28px dark:(bg-#363636 hover:!text-#fff) !px-14px !text-12px !text-#A8AFBF !dark:border-transparent !hover:bg-#eee4 !dark:text-#727272 !dark:hover:bg-#fff2"
                        border="~ !#CBD5E1"
                        :loading="item?.updateChatLoading"
                        :disabled="loading"
                        @click="saveEdit(item)"
                      >
                        {{ $t('chatContent.editorItemSave') }}
                      </el-button>
                      <el-button
                        :disabled="loading"
                        class="font-Lexend !m-0 !h-28px !px-14px !text-12px"
                        type="primary"
                        color="#000000"
                        @click="sendEdit(item)"
                      >
                        {{ $t('chatContent.editorItemSend') }}
                      </el-button>
                    </div>
                  </div>
                  <div v-else-if="!item.content.includes('<artifacts-tweak>')">
                    <RenderReferences :references="item.content" />
                    <div
                      v-if="!markdownViewStates[item.unionid]"
                      class="break-word content-transition whitespace-pre-wrap leading-normal"
                      :style="getContentStyle(item)"
                      :data-content-id="item.unionid"
                      v-text="keepPureInput(item.content)"
                    />
                    <div
                      v-else
                      class="prose max-w-full"
                      v-html="formatMarkdown({ string: item.content, receiving: true })"
                    >
                    </div>
                  </div>
                  <div v-else>
                    <div v-html="formatMarkdown({ string: item.content, receiving: true })"></div>
                  </div>
                  <div
                    v-if="item.contentTruncated"
                    class="mb-2 mt-2"
                  >
                    <SimpleButton class="!h-32px !px-12px !text-12px !leading-32px" @click="expandContent(item)">
                      {{ $t('chatContent.viewFullMessage') }}
                    </SimpleButton>
                  </div>
                  <div
                    class="grid cols-2 mt-12px w-full gap-x-2rem gap-y-1.25rem lt-md:(cols-1)"
                  >
                    <template
                      v-if="item.attachments && item.attachments.length > 0"
                    >
                      <AttachmentCard
                        v-for="f in item.attachments"
                        :key="f.fileId"
                        class="cursor-pointer"
                        :file-id="f.fileId"
                        :file-name="f.fileName"
                        :show-type="true"
                        :only-display="true"
                        @click="openAttachment(f)"
                      >
                      </AttachmentCard>
                    </template>

                    <div
                      v-if="(
                        !isGenerateImage
                        || [MODE_TYPE.GEMINI20, ...canModifyPicModels].includes(selectedMode?.type || 0)
                      ) && item.thumbnailUrls"
                      :class="[
                        item.thumbnailUrls.length > 1
                          ? 'flex-y-c cursor-default b-rd-[0.56rem]  bg-#fff p-[0.63rem] shadow-[0_0_2.5rem_0_#0000000f] transition-all w-fit min-w-full'
                          : '',
                      ]"
                    >
                      <div :class="[item.thumbnailUrls.length > 1 ? 'grid grid-cols-4 w-full gap-10px' : 'flex-y-c flex-grow-1']">
                        <!-- 这个图片原本的点击事件 @click="openImageGallery({ img: getFullImageUrl(img), imgIndex: index })" -->
                        <!-- 现在移交给FullscreenImage展示，自动收集并赋予点击事件 -->
                        <img
                          v-for="img in item?.questionOriginalUrls?.length ? item.questionOriginalUrls : item.thumbnailUrls"
                          :key="`${item.id}-${img}`"
                          :class="[
                            item.thumbnailUrls.length > 1
                              ? 'h-2.5rem min-w-2.5rem'
                              : 'w-full h-auto max-h-11.25rem',
                          ]"
                          class="att-img w-full cursor-pointer b-rd-0.63rem object-cover shadow-[0_0.31rem_0.31rem_0_#00000014] transition-all hover:shadow-[0_0_20px_#0000001A]"
                          :src="getFullImageUrl(img)"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else>
                  <div v-if="[MODE_TYPE.GEMINI20].includes(selectedMode?.type || 0)">
                    <div
                      :key="`${item.unionid}-${i18n.global.locale.value}`"
                      v-keep-html="
                        formatMarkdown({
                          string: getContent(item.content).string,
                          receiving: item.receiving,
                        })
                      "
                      :data-id="item.id"
                    >
                    </div>
                    <CustomImage
                      v-if="sections[index] && sections[index].img"
                      :key="item.id"
                      v-model:img="sections[index].img"
                      :hover="true"
                      :src="item.img"
                      :wrapper="sectionScroll"
                    ></CustomImage>
                  </div>

                  <div v-else-if="isGenerateImage">
                    <!-- @click-img="openImageGallery(sections[index])" -->
                    <!-- 禁用点击事件，交给FullscreenImage那套处理方式 -->
                    <!-- TODO：删除ImageGallery的相关代码，因为已经交给FullscreenImage处理了 -->
                    <CustomImage
                      v-if="sections[index]"
                      :key="item.id"
                      v-model:img="sections[index].img"
                      v-model:img-text="sections[index].imgText"
                      :hover="true"
                      :src="item.img"
                      :wrapper="sectionScroll"
                    ></CustomImage>
                  </div>

                  <div
                    v-else
                    :key="`${item.unionid}-${i18n.global.locale.value}`"
                    v-keep-html="
                      formatMarkdown({
                        string: getContent(item.content).string,
                        receiving: item.receiving,
                      })
                    "
                    :data-id="item.id"
                  >
                  </div>
                  <!-- 展示正在生成中的Artifacts卡片（只是为了展示卡片的流式输出） -->
                  <div v-if="item.artifact" class="mb-10px">
                    <div
                      class="card-button-generating relative w-280px select-none overflow-hidden rounded-15px p-5px before:(absolute left-0 top-0 h-full w-[200%] animate-[rainbow-border_4s_linear_infinite] bg-[length:50%_100%] bg-[linear-gradient(115deg,#4fcf70,#fad648,#a767e5,#12bcfe,#44ce7b)] content-empty z-0!)"
                    >
                      <div class="relative flex rounded-10px bg-[var(--artifacts-card-bg)] p-10px text-[var(--artifacts-card-text)] transition-all z-1! light:shadow-[0_0_40px_0_#0000000f]">
                        <section class="artifacts-card">
                          <img
                            :src="artifactsImg"
                            class="artifacts-icon aspect-ratio-16/12 w-16px"
                            alt=""
                            data-juchats="true"
                          />
                        </section>
                        <div class="flex flex-1 flex-col justify-between overflow-hidden py-2px pl-10px">
                          <div class="text-12px text-[var(--artifacts-card-desc)] font-400 font-RobotoSlab">
                            {{ item?.artifact?.title }}
                          </div>

                          <div class="truncate text-12px text-[var(--artifacts-card-desc)] font-400 font-RobotoSlab">
                            {{ item?.artifact?.description }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <template v-else>
                <VoiceBar
                  v-for="file in item.file"
                  :key="file.chats"
                  class="mb-5"
                  :receiving="item.receiving"
                  :file="file"
                ></VoiceBar>
              </template>
              <div class="-mt-6px space-y-10px">
                <!-- <div>
                  <span :class="{ 'text-green-500': showSharkAnimation }"> 条件1  {{ showSharkAnimation }}。</span>
                  <span :class="{ 'text-green-500': shouldShowToolAnimation(index) }"> 条件2  {{ shouldShowToolAnimation(index) }}。</span>
                </div> -->

                <!-- showSharkAnimation表示后端返回了工具调用信息 -->
                <!-- shouldShowToolAnimation返回前端逻辑中，是否该显示工具调用动画了 -->
                <SharkTextAnimation
                  v-if="showSharkAnimation && shouldShowToolAnimation(index)"
                  :tool-name="currentToolName"
                />
              </div>

              <!-- 如果没有联网内容，才显示在这个地方（对话底部） -->
              <div v-if="!getContent(item.content).array.length">
                <ChatContentItemAction
                  v-bind="getChatActionProps(item, index)"
                  :is-content-analyze-panel-visible="contentAnalyzePanelVisible"
                  v-on="getChatActionListeners(item)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- ------------------- -->
      <div
        v-if="sectionSkeletonVisible"
        class="chat_item chat_item-style"
        :class="{
          'chat_item-artifacts': artifactsVisible,
          'chat_item_margin': isResizablePanelVisible,
        }"
      >
        <el-skeleton
          :style="{
            '--el-skeleton-circle-size': isPc ? '50px' : '36px',
          }"
        >
          <template #template>
            <div class="mb-[50px] flex">
              <div class="shrink">
                <el-skeleton-item variant="circle" />
              </div>
              <el-skeleton class="ml-[20px]" :rows="2" animated />
            </div>
          </template>
        </el-skeleton>
      </div>
      <div v-if="sectionShadow && !(sendBoxFocus && !isRealPc)" class="chat-view__section__shadow"></div>
      <div v-if="sectionShadow" class="chat-view__section__placeholder"></div>
    </div>
    <ImageGallery
      v-model:visible="galleryVisible"
      :imgs="galleryImages"
      :index="imgActiveIndex"
      :mode-name="selectedMode?.showName"
    ></ImageGallery>
    <!-- 非Vision模型时，展示全屏图片 -->
    <FullscreenImage
      v-model:current-index="currentImageIndex"
      :show-fullscreen-image="showFullscreenImage"
      :images="previewImages"
      :prompt="previewImages[currentImageIndex]?.prompt || ''"
      @close="closeFullscreenImage"
    />
    <ArtifactsCodeHtml
      ref="artifactsCodeHtmlRef"
      v-model:visible="artifactsHtmlVisible"
      v-model:tab="artifactsTab"
      :is-share-page="isSharePage"
      :html-code="artifactsData"
      :llm-streaming="llmStreaming"
      :llm-generating="loading"
    />
    <AttachmentPanel
      v-if="currentChatAttachment.length > 0"
      ref="attachmentPanelRef"
      v-model:visible="attachmentPanelVisible"
      v-model:tab="attachmentPanelTab"
    />
    <ContentAnalyzePanel
      v-model:visible="contentAnalyzePanelVisible"
      v-model:data="contentAnalyzePanelData"
      @related="onRelated"
      @related-link="e => sendQuoteQuestion(e.title, e.link)"
    />
    <ProteinPanel
      v-model:visible="proteinPanelVisible"
      :pdb-id="proteinPanelPdbId"
    />
    <Transition
      enter-active-class="transition duration-300 ease-out"
      enter-from-class="translate-y-full opacity-0"
      enter-to-class="translate-y-0 opacity-100"
      leave-active-class="transition duration-200 ease-in"
      leave-from-class="translate-y-0 opacity-100"
      leave-to-class="translate-y-full opacity-0"
    >
      <div
        v-if="showBottomTrigger"
        class="absolute bottom-[100px] left-0 z-24 w-full flex items-center justify-center"
        @click="scrollBottom"
      >
        <div class="inline-block h-[32px] w-[32px] flex cursor-pointer items-center justify-center border-1px border-#E7E9ED rounded-full rounded-full bg-white p-[5px] transition-all duration-300 dark:(border-#272727) hover:shadow-[0_5px_10px_rgba(0,0,0,0.1)]">
          <svg width="12px" height="12.342203px" viewBox="0 0 12 12.342203" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
            <g id="Juchats-Dev-Thinking" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
              <g id="Website-Index-Model-Thinking" transform="translate(-1156, -919)" fill="#000000" fill-rule="nonzero">
                <g id="Return-to-the-bottom" transform="translate(1146, 909)">
                  <g id="编组" transform="translate(10, 10)">
                    <polygon id="路径" points="6.77137997 9.38902238 10.9091035 5.25129882 12 6.34219529 5.99999229 12.342203 0 6.34219529 1.0909119 5.25129882 5.2286046 9.38902238 5.2286046 0 6.77137997 0"></polygon>
                  </g>
                </g>
              </g>
            </g>
          </svg>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import type { ImageEventDetail } from '@/common/imageHandler'
import { nanoid } from 'nanoid'
import tippy from 'tippy.js'
import artifactsImg from '@/assets/images/artifacts-code.png'
import { copy, enableMacScrollbar, generateUUID, getSecondLevelDomain } from '@/common'
import services from '@/common/axios'
import { CelHiveLinkSplitSymbol } from '@/common/constant'
import { collectPreviewImages, initGlobalImageHandlers } from '@/common/imageHandler'
import { formatMarkdown } from '@/common/marked'
import { transformer } from '@/common/markmap'
import messageDB from '@/common/message'
import { svgText } from '@/common/svgText'
import { generateLetterId, getFullImageUrl, notify } from '@/common/tools'
import ContentAnalyzePanel from '@/components/ContentAnalyzePanel.vue'
import ProteinPanel from '@/components/ProteinPanel.vue'
import RenderReferences from '@/components/RenderReferences.vue'
import SmilesRenderer from '@/components/SmilesRenderer.vue'
import { mixpanel } from '@/config/mixpanel'
import { IMAGE_SPLIT, MODE_TYPE } from '@/enum'
import { useGlobalI18n } from '@/hooks/useGlobalI18n'
import i18n from '@/i18n'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { removeCitation } from '@/utils'
import 'tippy.js/dist/tippy.css'

const props = defineProps({
  sections: {
    type: Array as any,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  skeletonVisible: {
    type: Boolean,
    default: false,
  },
  sectionSkeletonVisible: {
    type: Boolean,
    default: false,
  },
  sectionShadow: {
    type: Boolean,
    default: false,
  },
  messageId: {
    type: [Number, String],
    default: '',
  },
  isSharePage: {
    type: Boolean,
    default: false,
  },
  // 用于标识 父组件是否正在一波一波的往sections中添加数据
  isBatchLoading: {
    type: Boolean,
    default: false,
  },
  sendBoxFocus: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits([
  'update:sections',
  'update:enableScroll',
  'send',
  'resend',
  'retry',
  'deploy',
])

// 用于标识Artifact是否正在生成，不代表整个LLM的生成状态，只标识html代码的输出状态
const llmStreaming = defineModel('llmStreaming', { type: Boolean, default: false })

const t = useGlobalI18n()
const { isRealPc } = useDevice()
const appStore = useAppStore()
const {
  modelSelectInfo,
  selectedMode,
  isGenerateImage,
  isPc,
  isClient,
  currentArtifacts,
  currentChatAttachment,
  chatViewSectionWidth,
  collapseCodeCounter,
  isResizablePanelVisible,
} = storeToRefs(appStore)
const { userInfoV2 } = storeToRefs(useUserStore())

const galleryVisible = ref(false)
const imgActiveIndex = ref(0)

const sectionScroll = ref()

const sectionContent = ref()

// 保存每个popover的引用
const popoverRefs = new Map()

const timerMap = ref(new Map())

// 编辑状态相关变量
const editingItem = ref<any>(null) // 当前正在编辑的消息
const editingContent = ref('') // 编辑中的内容

const showSharkAnimation = ref(false)
const currentToolName = ref('')

const artifactsVisible = ref(false)
const { artifactsHtmlVisible } = storeToRefs(appStore)
const artifactsData = ref('')
const artifactsTab = ref<'code' | 'preview'>('code')

const showFullscreenImage = ref(false)
const currentImageIndex = ref(0)

const previewImages = ref<{ src: string, text: string, prompt: string }[]>([])

const markdownViewStates = reactive<Record<string, boolean>>({})

const getContent = (markdown: string) => {
  if (typeof markdown !== 'string') {
    return {
      string: '',
      array: [],
    }
  }

  let object: any = {}
  const arr = markdown.split(IMAGE_SPLIT)
  let contentString = markdown.split('HERMSTDUIO')[0]
  if (arr.length > 1) {
    contentString = arr[1]
  }

  // 检测内容是否为HTML格式，同时排除前端手动添加的错误提示等
  // const isHtml = /<[a-z][\s\S]*>/i.test(contentString)
  //   && !contentString.includes('```') // 排除代码块中的HTML
  //   && !contentString.includes('data-IOcIq6iIsK') // 排除错误提示按钮
  //   && selectedMode.value?.type !== MODE_TYPE.ARTIFACTS

  // 指定模型DEEPSEEK、DEEPSEEKR1处理<think>标签
  if (selectedMode.value && [MODE_TYPE.DEEPSEEK, MODE_TYPE.DEEPSEEKR1].includes(selectedMode.value.type) && contentString) {
    // 将所有的<think>标签替换为```thinking
    contentString = contentString.replace(/<think>/g, '\n```thinking').replace(/<\/think>/g, '```')
    // 如过think为空，则不显示
    contentString = contentString.replace(/```thinking\n\n```/g, '')
  }

  // 处理联网搜索的数据
  try {
    let { searchResult, relatedSearchQueries, searchResults, recommend }
      = JSON.parse(markdown.split('HERMSTDUIO')[1])
    if (!searchResults) {
      searchResults = []
    }
    if (!recommend) {
      recommend = []
    }
    object = {
      string: contentString,
      array: searchResult ? JSON.parse(searchResult) : searchResults,
      relatedSearchQueries:
        relatedSearchQueries
        || (recommend.length
          ? [...recommend[0].matchAll(/\d+\.\s*(.+)/g)].map(match => match[1])
          : []),
    }
  }
  catch {
    object = {
      string: contentString,
      array: [],
    }
  }

  // 处理 Artifacts E2B 模型的数据
  try {
    if (JSON.parse(markdown)?.commentary) {
      const { commentary, title, code, file_path, template }
        = JSON.parse(markdown)
      object = {
        string: commentary,
        title,
        code,
        filePath: file_path,
        template,
        array: [],
      }
    }
  }
  catch {}

  return {
    ...object,
    array: object.array.filter((element: any) => element.link),
  }
}

const getText = (id: string) => {
  const target = document.querySelector(`[data-id="${id}"]`) as HTMLDivElement
  if (!target) {
    return ''
  }
  const cloned = target.cloneNode(true) as HTMLDivElement
  cloned.querySelectorAll('.language-thinking,.juchats-thinking').forEach((element) => {
    element.remove()
  })
  if (!target) {
    throw new RangeError(`target not found: ${id}`)
  }
  // eslint-disable-next-line unicorn/prefer-dom-node-text-content
  return cloned.innerText || ''
}

const attachmentPanelRef = ref()
const attachmentPanelVisible = ref(false)
const attachmentPanelTab = ref<'list' | 'preview'>('list')
const canModifyPicModels = [MODE_TYPE.GPT_IMAGE, MODE_TYPE.FLUX_KONTEXT_PRO]
const contentAnalyzePanelVisible = ref(false)
const contentAnalyzePanelData = ref({
  searchResults: [] as {
    host: string
    title: string
    link: string
    snippet: string
    position: number
    date?: undefined
  }[],
  relatedSearchQueries: [] as string[],
})

// 蛋白质侧边栏相关状态
const proteinPanelVisible = ref(false)
const proteinPanelPdbId = ref('')

function openAttachment(item: any) {
  attachmentPanelVisible.value = true
  attachmentPanelRef.value?.previewFile(item)
}

// ChatContentItemAction的参数比较多，但是由于需求要求的dom位置层级不一样需要在多个地方写了之后条件渲染，所以这里把参数抽离出来
const getChatActionProps = (item: any, index: number) => {
  return {
    item,
    index,
    loading: props.loading,
    sections: props.sections,
    copyVisible: Boolean(getContent(item.content).string.trim()),
    editorVisible: Boolean(getContent(item.content).string.trim()) && !item.receiving,
    regenerateVisible: !props.loading,
    markMapVisible: Boolean(hasValidMarkmapContent(getContent(item.content).string)),
    deleteVisible: Boolean(item.id),
    markMapContent: getContent(item.content),
    contentAnalyzeVisible: hasContentAnalyzeVisible(item),
  }
}

const getChatActionListeners = (item: any) => {
  return {
    copyItem: () => copyItem(item),
    copyAnswer: () => copyAnswer(getContent(item.content).string),
    resend: () => resend(item),
    deleteChat: () => deleteChat(item),
    closeMindMap: () => handleMarkMapClosed(item.unionid),
    resendOtherModel: (resendModelId: number) => resend(item, resendModelId),
    editorItem: () => startEditing(item),
    contentAnalyze: () => openContentAnalyze(item),
    closeContentAnalyze: () => handleCloseContentAnalyze(),
    toggleRenderMode: () => toggleRenderMode(item.unionid),
  }
}

const toggleRenderMode = (unionid: string) => {
  markdownViewStates[unionid] = !markdownViewStates[unionid]
  appStore.triggerCodeCollapse()
}

// 更新用户的提问
async function updateChatContent(id: number) {
  await services.post('/gpt/updQuestion', {
    id,
    question: editingContent.value,
  })
}

const deleteChat = async ({ id }: any) => {
  mixpanel.deleteChatRecord()
  await services.post('/gpt/delChat', {
    id,
  })

  const lastMessage = props.sections[props.sections.length - 1]
  if (lastMessage && lastMessage.id === id) {
    messageDB.deleteMessageById(props.messageId)
  }

  emits(
    'update:sections',
    // 刚发出去的用户信息没有id，ai返回的信息存在id，所以当操作删除AI消息时，如果前一项如果满足没有id && receiving为false，则一并删除前一项
    props.sections.filter((element: any, index: number) => {
      // 如果当前项的id与要删除的id相同，返回false删除此项
      if (element.id === id) {
        // 获取前一项
        const prevItem = props.sections[index - 1]
        if (prevItem && !prevItem.receiving && !prevItem.id) {
          // 直接返回false，这样前一项和当前项都会被过滤掉
          return false
        }
        return false
      }
      // 如果当前项的下一项存在，且下一项的id等于要删除的id，且当前项是用户消息且没有id
      const nextItem = props.sections[index + 1]
      if (nextItem && nextItem.id === id && !element.receiving && !element.id) {
        return false
      }
      return true
    }),
  )
}
const changePage = async ({ type, unionid, length }: any) => {
  emits(
    'update:sections',
    props.sections.map((element: any) => {
      if (element.unionid === unionid && element.receiving) {
        if (type === 'prev' && element.page === 1) {
          return element
        }
        if (type === 'next' && element.page >= length) {
          return element
        }
        const page = type === 'next' ? element.page + 1 : element.page - 1
        return {
          ...element,
          unionid: generateUUID(),
          ...element.children[page - 1],
          page,
        }
      }
      else {
        return element
      }
    }),
  )
}

const currentModelString = computed(() => {
  return Array.isArray(modelSelectInfo.value.modelName)
    ? modelSelectInfo.value.modelName.join(' | ')
    : modelSelectInfo.value.modelName || ''
})
function sendQuestion(string: string) {
  emits('send', string)
}

function sendQuoteQuestion(title: string, link: string) {
  sendQuestion(`" 引用：[${title}](${link}) "深度分析！`)
}

const closeFullscreenImage = () => {
  showFullscreenImage.value = false
  currentImageIndex.value = 0
}

// 处理图片预览事件的函数
const handlePreviewImage = (event: CustomEvent<ImageEventDetail>) => {
  const targetSrc = event.detail.src
  const index = previewImages.value.findIndex(img => img.src === targetSrc)
  if (index !== -1) {
    currentImageIndex.value = index
    showFullscreenImage.value = true
  }
}

// 更新预览图片列表
const updatePreviewImages = () => {
  // 获取所有图片元素
  const images = sectionContent.value?.querySelectorAll('img:not(.net-icon)') || []
  previewImages.value = collectPreviewImages(images, props.sections)
}

// 监听sections变化
watch(
  () => props.sections,
  () => {
    nextTick(() => {
      // 更新预览图片列表
      updatePreviewImages()
      // 检查内容高度
      checkContentHeight()
    })
  },
  { deep: true },
)

watch(() => props.messageId, () => {
  contentAnalyzePanelVisible.value = false
  proteinPanelVisible.value = false
})

// 折叠所有 pre 块，除了 mermaid 和 thinking
const collapseAllPreBlocks = () => {
  nextTick(() => {
    if (sectionScroll.value) {
      const preElements = sectionScroll.value.querySelectorAll('pre')
      preElements.forEach((pre: HTMLPreElement) => {
        const codeElement = pre.querySelector('code')
        if (codeElement) {
          const isMermaid = codeElement.classList.contains('language-mermaid')
          const isSmiles = codeElement.classList.contains('language-smiles')
          const isThinking = codeElement.classList.contains('language-thinking')
          if (!isMermaid && !isThinking && !isSmiles) {
            pre.classList.add('code-height-max')
          }
        }
        else {
          // 如果 pre 元素下没有 code 元素，也折叠
          pre.classList.add('code-height-max')
        }
      })
    }
  })
}

watch(() => props.loading, (newVal, oldVal) => {
  if (oldVal === true && newVal === false) {
    collapseAllPreBlocks()
  }
})

watch(collapseCodeCounter, () => {
  setTimeout(() => {
    collapseAllPreBlocks()
  }, 100)
})

watch(() => artifactsHtmlVisible.value, (val) => {
  if (val) {
    if (contentAnalyzePanelVisible.value) {
      contentAnalyzePanelVisible.value = false
    }
    if (proteinPanelVisible.value) {
      proteinPanelVisible.value = false
    }
  }
})

watch(() => contentAnalyzePanelVisible.value, (val) => {
  if (val) {
    if (artifactsHtmlVisible.value) {
      artifactsHtmlVisible.value = false
    }
    if (proteinPanelVisible.value) {
      proteinPanelVisible.value = false
    }
  }
})

watch(() => proteinPanelVisible.value, (val) => {
  if (val) {
    if (artifactsHtmlVisible.value) {
      artifactsHtmlVisible.value = false
    }
    if (contentAnalyzePanelVisible.value) {
      contentAnalyzePanelVisible.value = false
    }
  }
})

const scrollBottom = () => {
  const interval = setInterval(() => {
    if (sectionScroll.value) {
      sectionScroll.value.addEventListener('scroll', () => {
        if (
          sectionScroll.value
          && Math.ceil(
            sectionScroll.value.scrollTop + sectionScroll.value.clientHeight,
          ) < sectionScroll.value.scrollHeight
        ) {
          emits('update:enableScroll', false)
        }
        else {
          emits('update:enableScroll', true)
        }
      })
      sectionScroll.value.scrollTop = sectionScroll.value.scrollHeight
      clearInterval(interval)
    }
  })
}

const addCopyListener = () => {
  sectionScroll.value && sectionScroll.value.addEventListener('click', copyListener)
}

const removeCopyListener = () => {
  sectionScroll.value && sectionScroll.value.removeEventListener('click', copyListener)
}

const copyListener = async (event: Event) => {
  const target = event.target
  if (
    target instanceof HTMLElement
    && target.classList.contains('hljs__copy__btn')
  ) {
    // 使用data属性存储按钮的唯一ID
    let btnId = target.getAttribute('data-copy-id')
    if (!btnId) {
      btnId = generateLetterId()
      target.setAttribute('data-copy-id', btnId)
    }

    // 清除这个按钮之前的定时器
    if (timerMap.value.has(btnId)) {
      clearTimeout(timerMap.value.get(btnId))
    }

    const { copy } = useClipboard({
      source: decodeURIComponent(target.getAttribute('value') || ''),
    })
    await copy()

    const copyIcon = target.querySelector('.copy-icon')
    const textSpan = target.querySelector('span')
    if (copyIcon) {
      copyIcon.innerHTML = svgText.check
    }
    if (textSpan) {
      textSpan.textContent = 'Copied!'
    }
    target.classList.add('copied')

    // 存储新的定时器
    const newTimer = setTimeout(() => {
      if (copyIcon) {
        copyIcon.innerHTML = svgText.copy
      }
      if (textSpan) {
        textSpan.textContent = 'Copy code'
      }
      target.classList.remove('copied')
      timerMap.value.delete(btnId)
    }, 1000)

    timerMap.value.set(btnId, newTimer)
    notify.success({ title: t('imageGallery.copySuccess').value })
  }
}

const addRetryListener = () => {
  sectionScroll.value && sectionScroll.value.addEventListener('click', retryListener)
}

const removeRetryListener = () => {
  sectionScroll.value && sectionScroll.value.removeEventListener('click', retryListener)
}

const { width: chatViewSectionWidthCom } = useElementSize(sectionScroll)

watchEffect(() => {
  if (chatViewSectionWidthCom.value) {
    chatViewSectionWidth.value = chatViewSectionWidthCom.value
  }
})

const retryListener = async (event: Event) => {
  const target = event.target
  if (target instanceof HTMLElement && target.classList.contains('retry-button')) {
    const { content, uuid, thumbnailUrls, attachments } = await messageDB.getMessageById(props.messageId)
    // 删除所有带有 retry 标记的内容及和带有retry 标记相同 requestId 的对话
    let _sections = props.sections
    const retryRequestIds = _sections
      .filter((item: any) => item.retry)
      .map((item: any) => item.requestId)
    _sections = _sections.filter((item: any) => {
      return !retryRequestIds.includes(item.requestId)
    })
    emits('update:sections', _sections)
    // 发送重试请求
    emits('retry', { content, uuid, thumbnailUrls, attachments })
  }
}

const copyQuestion = (content: string) => {
  copy(content)
  mixpanel.copyQuestion()
}

const copyAnswer = (content: string) => {
  const juchatsThinking = /<juchats-thinking>[\s\S]*?<\/juchats-thinking>/g
  const juchatsArtifacts = /<juchats-artifacts>[\s\S]*?<\/juchats-artifacts>/g
  const regex = /```thinking[\s\S]*?```/g
  const result = removeCitation(content)
    .replace(regex, '')
    .replace(juchatsThinking, '')
    .replace(juchatsArtifacts, '')
    .trim()

  copy(result)
  mixpanel.copyAnswer()
}

// 从question字段中获取uuid
function getQuestionImgUUID(str: string) {
  if (!str || typeof str !== 'string' || !str.includes('--IMAGES-SPLIT--')) {
    return ''
  }

  const parts = str.split('--IMAGES-SPLIT--')
  return parts[0] || ''
}

const resend = (item: any, resendModelId?: number, retryChatId?: number) => {
  let uuid = ''
  // 先试图从item中获取uuid（历史记录接口获取回来的可能有）
  if (item.uuid) {
    uuid = item.uuid
  }
  // 如果item中没有uuid，则从item.question中获取，（历史记录接口获取回来的有）
  else if (getQuestionImgUUID(item.question) !== '') {
    uuid = getQuestionImgUUID(item.question)
  }
  // 如果不是从历史记录接口获取回来的（用户新创建的聊天），item.question的值将会是纯用户文字，就去找当前项的前一项item.receiving为假值的thumbnailUrls数组中取uuid（通过unionid确定位置）
  // thumbnailUrls数组每一项都是形如https://oss.xxxxxx.com/picture/8fc29512302380cc515c5c.webp
  // 取每一项的文件名，去掉.webp后缀，就是uuid，然后用逗号拼接起来
  else {
    // 找到当前item在sections中的索引
    const currentIndex = props.sections.findIndex((i: any) => i.unionid === item.unionid)
    // 从当前索引向前查找，找到第一个receiving为false的项
    const prevItem = props.sections
      .slice(0, currentIndex)
      .reverse()
      .find((i: any) => !i.receiving)

    if (prevItem && prevItem.thumbnailUrls) {
      uuid = prevItem.thumbnailUrls
        .map((url: string) => {
          return url
            .split('/')
            .pop()
            ?.split('.')[0]
        })
        .join(',')
    }
  }
  item.uuid = uuid

  // 获取并关闭对应的popover
  // const popover = popoverRefs.get(item.unionid)
  // if (popover) {
  //   popover.hide()
  // }

  handleMarkMapClosed(item.unionid)
  emits('resend', item, resendModelId, retryChatId)
}

const onRelated = (question: string) => {
  emits('send', question)
  contentAnalyzePanelVisible.value = false
}

const openContentAnalyze = (item: any) => {
  try {
    const data = JSON.parse(item.content.split('HERMSTDUIO')[1])
    contentAnalyzePanelData.value = {
      searchResults: JSON.parse(data.searchResult).map((item: any) => {
        const url = new URL(item.link)
        return {
          ...item,
          host: url.host,
        }
      }),
      relatedSearchQueries: data.relatedSearchQueries,
    }
    contentAnalyzePanelVisible.value = true
  }
  catch (error) {
    notify.warning({ title: t('chatContent.lossContent').value })
    console.error(error)
  }
}

const handleCloseContentAnalyze = () => {
  contentAnalyzePanelVisible.value = false
}

const keepPureInput = (content: string) => {
  let pureStr = content

  if (content.includes(IMAGE_SPLIT)) {
    const [, ...rest] = content.split(IMAGE_SPLIT)
    pureStr = rest.join('')
  }

  if (content.includes(CelHiveLinkSplitSymbol)) {
    const [, ...rest] = content.split(CelHiveLinkSplitSymbol)
    pureStr = rest.join('')
  }

  if (content.includes('/ecp-search')) {
    pureStr = pureStr.replace(/\/ecp-search/g, '')
  }

  if (content.includes('/ecp')) {
    pureStr = pureStr.replace(/\/ecp/g, '')
  }

  const reg = /```references[\s\S]*?```/g
  pureStr = pureStr.replace(reg, '')

  return pureStr.trim()
}

// 检查内容是否可以生成思维导图
function hasValidMarkmapContent(str: string) {
  const content = str?.trim() || ''
  if (!content) {
    return false
  }

  try {
    // 使用 transformer.transform 来解析内容
    const { root } = transformer.transform(content)
    return root?.children.length > 0
  }
  catch {
    return false
  }
}

const hasContentAnalyzeVisible = (item: any) => {
  try {
    const data = JSON.parse(item.content.split('HERMSTDUIO')[1])
    return JSON.parse(data.searchResult).length > 0
  }
  catch {
    return false
  }
}

const isSmiles = (content: string) => {
  return content.includes('language-smiles')
}

const extractSmiles = (content: string) => {
  const match = content.match(/```smiles\n([\s\S]*?)\n```/)
  return match ? match[1].trim() : ''
}

/**
 * 处理ARTIFACTS工具调用，显示HTML代码
 * @param {string} html HTML代码
 * @param {boolean} isEnd 是否是工具调用结束
 */
const handleArtifactsToolCall = (html: string, isEnd = false) => {
  artifactsData.value = html
  artifactsHtmlVisible.value = true

  // 当工具调用结束时，自动切换到预览标签
  if (isEnd) {
    artifactsTab.value = 'preview'
    llmStreaming.value = false
  }
  else {
    artifactsTab.value = 'code'
    llmStreaming.value = true
  }
}

// 添加处理展示Artifacts HTML代码的事件处理函数
const handleArtifactsHtmlEvent = ((event: CustomEvent) => {
  const { detail: { element, index } } = event
  let _index = index
  const dataId = element.closest('[data-id]')?.getAttribute('data-id')
  const currentItem = props.sections.find((item: any) => String(item.id) === dataId)
  if (index >= currentItem?.artifactsList.length) {
    _index = currentItem?.artifactsList.length - 1
  }
  const artifact = currentItem?.artifactsList?.[_index]?.artifacts
  if (artifact) {
    const html = JSON.parse(artifact).html
    const title = JSON.parse(artifact).title
    const description = JSON.parse(artifact).description
    const id = currentItem?.artifactsList?.[_index].id
    currentArtifacts.value = {
      id,
      title,
      description,
      html,
    }
    artifactsData.value = html
    artifactsHtmlVisible.value = true
    nextTick(() => {
      artifactsTab.value = 'preview'
    })
  }
  else {
    artifactsHtmlVisible.value = true
  }
}) as EventListener

// 添加处理蛋白质面板的事件处理函数
const handleProteinPanelEvent = ((event: CustomEvent) => {
  const { detail: { pdbId } } = event
  proteinPanelPdbId.value = pdbId
  proteinPanelVisible.value = true
}) as EventListener

defineExpose({
  sectionScroll,
  sectionContent,
  scrollBottom,
  showSharkAnimation,
  currentToolName,
  handleArtifactsToolCall,
  artifactsData,
  closeArtifactsCode,
})

onMounted(() => {
  addCopyListener()
  addRetryListener()
  updatePreviewImages()
  initGlobalImageHandlers()

  // 添加图片预览事件监听
  window.addEventListener('preview-image', handlePreviewImage as EventListener)

  // 添加展示Artifacts HTML代码事件监听
  window.addEventListener('show-artifacts-html', handleArtifactsHtmlEvent)

  // 添加蛋白质面板事件监听
  window.addEventListener('show-protein-panel', handleProteinPanelEvent)

  // 初始检查内容高度
  checkContentHeight()
})

// 判断是否需要显示工具调用动画
const shouldShowToolAnimation = (index: number) => {
  // 一上来还没说话就开始调用工具（openai）
  const isToolCalledAtTop
    = index === props.sections.length - 1 && !props.sections[index].receiving

  // 在输出过程中调用工具（claude等）
  const isToolCalledNonTop
    = index === props.sections.length - 1 && props.sections[index].receiving

  // 二者符合其一即可显示
  return isToolCalledNonTop || isToolCalledAtTop
}

onBeforeUnmount(() => {
  removeCopyListener()
  removeRetryListener()
  // 移除图片预览事件
  window.removeEventListener('preview-image', handlePreviewImage as EventListener)

  // 移除展示Artifacts HTML代码事件
  window.removeEventListener('show-artifacts-html', handleArtifactsHtmlEvent)

  // 移除蛋白质面板事件
  window.removeEventListener('show-protein-panel', handleProteinPanelEvent)
})

const galleryImages = computed(() => {
  const arr = props.sections.filter((item: any) => {
    return item.img && item.receiving
  })

  arr.forEach((item: any, index: number) => {
    item.imgIndex = index
  })
  return arr.map((item: any) => {
    return {
      src: item.img,
      text: item.imgText,
    }
  })
})

const { showBottomTrigger } = enableMacScrollbar(sectionScroll)

// 监听用户头像变化，变化后更新对话列表中的用户头像
watch(() => userInfoV2.value.thumbnailUrl, (newAvatar) => {
  if (newAvatar && props.sections) {
    // 更新所有非AI消息的头像
    emits('update:sections', props.sections.map((section: any) => {
      if (!section.receiving) {
        return {
          ...section,
          avatar: newAvatar,
        }
      }
      return section
    }))
  }
}, { immediate: true })

// 开始编辑消息
const startEditing = (item: any) => {
  editingItem.value = item
  editingContent.value = item.content
}

// 查找聊天消息的ID
// 如果item没有id，则尝试在sections中找到相关联的AI回复消息的ID
const findChatId = (item: any) => {
  // 如果item已经有id，直接返回
  if (item.id) {
    return item.id
  }

  // 找到当前编辑的用户消息在sections中的索引
  const userMsgIndex = props.sections.findIndex((section: any) =>
    section.unionid === item.unionid,
  )

  // 如果找到了用户消息，并且在数组范围内可能有AI回复消息
  if (userMsgIndex !== -1 && userMsgIndex + 1 < props.sections.length) {
    // 检查下一个消息是否是AI的回复(receiving为true)
    const aiResponseMsg = props.sections[userMsgIndex + 1]

    if (aiResponseMsg && aiResponseMsg.receiving && aiResponseMsg.id) {
      // 返回AI回复消息的ID
      return aiResponseMsg.id
    }
  }

  // 如果没有找到相关ID，返回undefined
  return undefined
}

// 保存编辑后的消息
const saveEdit = async (item: any) => {
  if (editingContent.value === item.content) {
    notify.warning({ title: t('chatContent.noContentModification').value })
    return
  }
  if (editingContent.value.trim() === '') {
    notify.warning({ title: t('chatContent.invalidContent').value })
    return
  }

  item.updateChatLoading = true

  // 查找聊天ID
  const chatId = findChatId(item)
  item.id = chatId
  if (!chatId) {
    item.updateChatLoading = false
    notify.warning({ title: t('chatContent.dataError').value })
    return
  }
  await updateChatContent(chatId)

  item.updateChatLoading = false

  // 更新消息内容
  emits(
    'update:sections',
    props.sections.map((section: any) => {
      if (section.unionid === item.unionid) {
        return {
          ...section,
          content: editingContent.value,
          id: chatId,
        }
      }
      return section
    }),
  )
  resetEditing()
}

function resetEditing() {
  editingItem.value = null
  editingContent.value = ''
}

// 发送编辑后的消息
const sendEdit = (item: any) => {
  if (editingContent.value === item.content || editingContent?.value?.trim() === '') {
    notify.warning({ title: t('chatContent.invalidContent').value })
    return
  }

  // 更新内容（内存中更新，不影响UI）
  item.content = editingContent.value
  item.question = editingContent.value

  // 重置编辑状态
  resetEditing()

  // 查找聊天ID
  const chatId = findChatId(item)

  if (chatId) {
    // 从sections中过滤掉与当前项具有相同id的所有项
    emits(
      'update:sections',
      props.sections.filter((section: any) => section.id !== chatId),
    )

    // 调用resend函数，并添加retryChatId参数
    resend(item, undefined, chatId)
  }
  else {
    // 如果没有找到相关ID，只删除用户消息并重新发送
    const userMsgIndex = props.sections.findIndex((section: any) =>
      section.unionid === item.unionid,
    )

    if (userMsgIndex !== -1) {
      emits(
        'update:sections',
        props.sections.filter((section: any, index: number) =>
          index !== userMsgIndex,
        ),
      )
    }

    // 直接调用resend，不带retryChatId参数
    resend(item)
  }
}

// 取消编辑
const cancelEdit = () => {
  editingItem.value = null
  editingContent.value = ''
}

// 展开内容
const expandLimitHeight = 498
const expandContent = (item: any) => {
  const contentEl = document.querySelector(`[data-content-id="${item.unionid}"]`) as HTMLElement

  // 如果找不到元素，直接设置状态并返回
  if (!contentEl) {
    item.contentTruncated = false
    return
  }

  // 先获取实际高度
  const actualHeight = contentEl.scrollHeight
  // 设置具体的高度值，而不是直接移除max-height限制
  contentEl.style.maxHeight = `${expandLimitHeight}px`
  // 强制回流
  void contentEl.offsetHeight
  // 设置为实际高度
  contentEl.style.maxHeight = `${actualHeight}px`
  // 延迟后移除高度限制
  setTimeout(() => {
    item.contentTruncated = false
    contentEl.style.maxHeight = ''
  }, 500)
}

// 获取内容样式对象
function getContentStyle(item: any) {
  // 如果item不存在或未截断，返回空对象
  if (!item || !item.contentTruncated) {
    return {}
  }

  // 内容被截断，返回带有高度限制的样式
  return {
    maxHeight: `${expandLimitHeight}px`,
    overflow: 'hidden',
  }
}

// 检查内容高度并设置截断状态
const checkContentHeight = () => {
  nextTick(() => {
    props.sections.forEach((item: any) => {
      // 如果是AI消息，跳过当前项
      if (item.receiving) { return }
      const contentEl = document.querySelector(`[data-content-id="${item.unionid}"]`) as HTMLElement
      // 如果找不到对应元素，跳过当前项
      if (!contentEl) { return }
      // 如果高度不超过限制高度或已经设置了截断状态，跳过当前项
      if (contentEl.scrollHeight <= expandLimitHeight || item.contentTruncated !== undefined) { return }
      // 设置为截断状态
      item.contentTruncated = true
    })
  })
}

// 根据unionid关闭对应的popover
const handleMarkMapClosed = (unionid: string) => {
  if (!unionid) { return }

  // 获取并关闭对应的popover
  const popover = popoverRefs.get(unionid)
  if (popover) {
    popover.hide()
  }
}

const artifactsCodeHtmlRef = useTemplateRef('artifactsCodeHtmlRef')

// 收起Artifacts侧边栏
function closeArtifactsCode() {
  artifactsHtmlVisible.value = false
  artifactsCodeHtmlRef?.value?.initialize()
}

function copyItem(item: any) {
  if (item.receiving) {
    copyAnswer(getText(item.id))
  }
  else {
    copyQuestion(getContent(item.content).string.trim())
  }
}

const movedTip = new Set<HTMLElement>()
const handleContentIndex = (e: MouseEvent | TouchEvent, item: { content: string }) => {
  const ele = e.target as HTMLElement
  if (ele && ele.classList.contains('citation')) {
    if (movedTip.has(ele)) { return }

    movedTip.add(ele)
    ele.onmouseleave = () => {
      movedTip.delete(ele)
    }

    try {
      const index = +ele.textContent!
      const extraJSON = JSON.parse(item.content.split('HERMSTDUIO')[1])
      const searchResult = JSON.parse(extraJSON.searchResult)
      const target = searchResult.find((item: { position: number }) => item.position === index)
      const url = new URL(target.link)

      tippy(ele, {
        allowHTML: true,
        content: `<a ${isClient.value ? `href="javascript:void(0)" onclick="window.__TAURI__.shell.open('${target.link}')"` : `target="_blank" href="${target.link}"`} rel="noreferrer noopener" class="block border dark:border-0 border-[var(--s)]! rounded-5px py-16px px-18px bg-white dark:bg-[#272727] light:shadow-[0_0_40px_0_#0000000f] dark:shadow-[0_0_10px_0_#0000001a]">
          <div class="font-semibold text-dark dark:text-white text-16px mb-[10px]">${target.title}</div>
          ${target.snippet ? `<div class="font-Inter text-[12px] line-clamp-4 mb-[12px]">${target.snippet}</div>` : ''}
          <div class="font-Inter text-[12px] font-bold border-0! flex items-center">
            <div class="w-[20px] mr-[2px] flex items-center justify-center">
              <img class="w-[90%] rounded-full" src="//s2.googleusercontent.com/s2/favicons?domain=${url.toString()}" alt="">
            </div>
            <span>${url.host}</span>
          </div>
        </a>`,
        arrow: false,
        showOnCreate: true,
        touch: 'hold',
        placement: isPc.value ? 'right-end' : undefined,
        interactive: true,
        onHidden(instance) {
          instance.destroy()
        },
      })
    }
    catch (error) {
      console.error(error)
    }
  }
}
</script>

<style>
.chat_item-style.loading .citation {
  display: none;
}
</style>

<style lang="scss" scoped>
@import "@/assets/styles/chat-view";

.chat_item {
  // 780内容宽度 / 16px = 48.75rem
  @apply mx-auto md:w-48.75rem transition-cusbezier-300 lt-xl:w-75% lt-md:w-full ;
}

.chat_item-artifacts {
  @apply w-[90%] !important;
}

.chat_item-style {
  @apply py-24px pr-45px;

  .answer-content {
    @apply transition-cusbezier-300;
  }

  .open-model-select {
    @apply pb-240px;
  }
}

// 内容高度变化的过渡动画
.content-transition {
  transition: max-height 0.4s linear;
  will-change: max-height;
}

// CSS虚拟滚动，用于在加载数据期间，减少DOM的渲染优化性能
// 因为我们的每一项的高度不定，所以随便先给个300高吧，加载的时候滚动条乱跳就乱跳吧，加载完了去除这个class滚动条就正常了
.lazy {
  content-visibility: auto;
  contain-intrinsic-size: 300px;
}

.editor-chat-wrapper {
  :deep(.el-textarea__inner) {
    @apply border-none bg-transparent  shadow-none resize-none;
  }
}
</style>
